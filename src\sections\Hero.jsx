import React from 'react'
import { gsap } from 'gsap'
import { useGSAP } from '@gsap/react'
const Hero = () => {
  const tl = gsap.timeline()
  useGSAP(() => {
    tl.from('#hero-heading', {
      opacity: 0,
      y: 100,
      duration: 1,
      ease: "back.out(1.7)",
      delay: 1.5
    })
    tl.from('#hero-para', {
      opacity: 0,
      y: 100,
      duration: 1,
      ease: "back.inout(2)",
      delay: 1.6
    })
    tl.from('#word1', {
      opacity: 0,
      x: 100,
      ease: "back.inout(2)",
      
    })
    tl.from('#word2', {
      x:-100,
      opacity: 0,
      ease: "back.inout(2)",
      
    })
  })
  return (
    <section className='h-screen flex xl:flex-row flex-col-reverse items-center justify-between lg:px-24 px-10 relative overflow-hidden'>
    <div className="">
      <h1 className='text-5xl md:text-7xl lg:text-8xl font-bold' id="hero-heading">Translating <span className="bg-gradient-to-r from-primary to-gray-400 bg-clip-text text-transparent" id="word1"> vision </span> 
      <br/>into<br/> vibrant 
      <span className="bg-gradient-to-r from-primary to-gray-400 bg-clip-text text-transparent" id="word2"> interaction</span></h1>
      <p className='text-2xl md:text-1xl lg:text-4xl lg:mt-[6rem] text-text' id="hero-para">
      I build the clean, resilient, and <br /> maintainable code that makes great design feel effortless.
      </p>
    </div>
    </section>
  )
}

export default Hero