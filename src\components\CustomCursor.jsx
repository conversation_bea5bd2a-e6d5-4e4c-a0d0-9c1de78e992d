/* eslint-disable react-hooks/rules-of-hooks */
import React from 'react'
import {useRef , useEffect, useState} from 'react'

import {gsap} from 'gsap'
const CustomCursor = () => {

    // references for cursor elements
    const cursorRef = useRef(null);
    const cursorBorderRef = useRef(null);
    const [skillData, setSkillData] = useState({ icon: '', name: '' });

    // hide on mobiles and tablets
    const isMobile = typeof window != "undefined" &&
    window.matchMedia("(max-width:768px)").matches

    useEffect(()=>{
        const cursor = cursorRef.current;
        const cursorBorder = cursorBorderRef.current;

        if (!cursor || !cursorBorder) return;

        // Set initial position and transform origin
        gsap.set([cursor, cursorBorder], {
            xPercent: -50,
            yPercent: -50,
            x: 0,
            y: 0
        })

            const xTo = gsap.quickTo(cursor , "x" ,{
                duration:0.2 , ease:"power3.out"
            })
            const xBorderTo = gsap.quickTo(cursorBorder , "x" ,{
                duration:0.5 , ease:"power3.out"
            })
            const yTo = gsap.quickTo(cursor , "y" , {
                duration:0.2 , ease:"power3.out"
            })
            const yBorderTo = gsap.quickTo(cursorBorder , "y" , {
                duration:0.5 , ease:"power3.out"
            })

            const handleMove = (e) =>{
                xTo(e.clientX)
                yTo(e.clientY)
                xBorderTo(e.clientX)
                yBorderTo(e.clientY)
            }

            window.addEventListener("mousemove" , handleMove)

            document.addEventListener("mousedown" , ()=>{
                gsap.to([cursor , cursorBorder],{
                    scale:0.6,
                    duration:0.2
                })
            })
            document.addEventListener("mouseup" , ()=>{
                gsap.to([cursor , cursorBorder],{
                    scale:1,
                    duration:0.2
                })
            })
            
            // Handle skill hover effects
            const handleSkillHover = () => {
                const skillHover = document.body.hasAttribute('data-skill-hover');
                const skillIcon = document.body.getAttribute('data-skill-icon') || '';
                const skillName = document.body.getAttribute('data-skill-name') || '';
                
                if (skillHover) {
                    setSkillData({ icon: skillIcon, name: skillName });
                    // Hide inner cursor
                    gsap.to(cursor, {
                        opacity: 0,
                        duration: 0.3
                    })
                    
                    // Scale up border cursor
                    gsap.to(cursorBorder, {
                        scale: 4,
                        duration: 0.3
                    })
                }
            }
            
            const handleSkillLeave = () => {
                setSkillData({ icon: '', name: '' });
                // Show inner cursor
                gsap.to(cursor, {
                    opacity: 1,
                    duration: 0.3
                })
                
                // Reset border cursor
                gsap.to(cursorBorder, {
                    scale: 1,
                    duration: 0.3
                })
            }
            
            // Check for skill hover state periodically
            const interval = setInterval(() => {
                if (document.body.hasAttribute('data-skill-hover')) {
                    handleSkillHover()
                } else {
                    handleSkillLeave()
                }
            }, 100)

            // Cleanup function
            return () => {
                window.removeEventListener("mousemove", handleMove)
                clearInterval(interval)
            }

    },[])
  // Don't render cursor on mobile devices
  if (isMobile) {
    return null;
  }

  return (
    <>
        <div ref={cursorRef} className="fixed top-0 left-0 w-[20px] h-[20px] bg-text rounded-full pointer-events-none z-[999] mix-blend-difference"></div>
        <div ref = {cursorBorderRef} className="fixed top-0 left-0 w-[40px] h-[40px] border rounded-full border-text pointer-events-none z-[999] mix-blend-difference opacity-50 flex flex-col items-center justify-center p-2">
            {skillData.icon && (
                <div dangerouslySetInnerHTML={{ __html: skillData.icon }} />
            )}
            {skillData.name && (
                <span className="text-text text-xs font-bold mt-1 whitespace-nowrap">
                    {skillData.name}
                </span>
            )}
        </div>
    </>
  )
}

export default CustomCursor