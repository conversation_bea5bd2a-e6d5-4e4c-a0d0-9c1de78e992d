import React from 'react'

import {useRef , useEffect} from 'react'

import {gsap , ScrollTrigger} from '@gsap/react'

const Contact = () => {
  const titleRef = useRef(null)
  const sectionRef = useRef(null)

  useEffect(()=>{
    gsap.registerPlugin(ScrollTrigger)
      gsap.fromTo(titleRef.current ,{
        y:100 , opacity:0
      },
    {
      y:-300,opacity:1,duration:0.8,
      scrollTrigger:{
        trigger:sectionRef.current,
        start:"top 40%",
        toggleActions:"play none none reverse"
      }
    })
  })

  return (
    <section ref={sectionRef} className='h-screen relative overflow-hidden bg-background'>
      <div className="container mx-auto px-4 h-full flex flex-col items-center justify-center">
        <h1 ref={titleRef} className='text-4xl md:text-5xl lg:text-6xl font-bold text-text sm:mb-16 text-center opacity-0'>Contact Me</h1>
      </div>
    </section>
  )
}

export default Contact