import React from 'react'
import {useRef , useEffect} from 'react'

import {gsap} from 'gsap'
import { ScrollTrigger } from 'gsap/all'
import { FaLinkedinIn } from "react-icons/fa6";
import { FaGithub } from "react-icons/fa6";
import { FaDownload } from "react-icons/fa6";
import { SiLeetcode } from "react-icons/si";

const About = () => {
  const titleRef = useRef(null)
  const sectionRef = useRef(null)

  useEffect(()=>{
    gsap.registerPlugin(ScrollTrigger)
      gsap.fromTo(titleRef.current ,{
        y:300 , opacity:0
      },
    {
      y:0,opacity:1,duration:0.8,
      scrollTrigger:{
        trigger:sectionRef.current,
        start:"top 40%",
        toggleActions:"play none none reverse"
      }
    })
  })

  return (
    <section ref={sectionRef} className='h-full relative overflow-hidden bg-background'>
      <div className="mx-auto px-4 sm:px-10 md:px-[4rem] lg:px-[5rem] h-full flex flex-col">
        <div className="flex items-center justify-center py-8 md:py-12">
          <h1 ref={titleRef} className='text-4xl md:text-5xl lg:text-6xl font-bold text-text text-center opacity-0'>A Glimpse Into Me</h1>
        </div>
        
        {/* Two-column responsive layout */}
        <div className="flex-1 flex flex-col md:flex-row gap-4 md:gap-4 lg:gap-8 pb-8 lg:mt-[5rem]">
          {/* Left column */}
          <div className="flex-1 lg:flex-[7] rounded-lg p-4 min-h-[500px] md:min-h-full">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text">
              Hi , I'm <span className="outlinetext">Harshith</span>
            </h1>
            <p className="text-xl md:text-2xl lg:text-3xl lg:mt-[5rem] text-text mt-5">
              a graduating student with a strong interest in software development and problem-solving. 
              I enjoy building web applications, automating tasks, and creating efficient solutions that blend functionality with clean design. 
              My academic projects have helped me strengthen both my programming logic and practical development skills, preparing me to take on real-world challenges.
            </p>

            <p className="text-xl md:text-2xl lg:text-3xl lg:mt-[5rem] text-text mt-5">
              Currently, I’m focused on growing as a full-stack developer while continuously learning and improving my skills. 
              My long-term goal is to explore advanced areas like machine learning and AI once I gain more industry-level experience in development.
            </p>
            <p className="text-xl md:text-2xl lg:text-3xl lg:mt-[5rem] text-text mt-5">
              My hobbies include playing chess, listening to music, and plaing video games in my free time.
            </p>
          </div>
          
          {/* Right column */}
          <div className="flex-1 lg:flex-[3] rounded-lg p-4 min-h-[200px] md:min-h-full">
            <button className="bg-primary text-text px-8 py-4 rounded-lg text-2xl font-bold hover:bg-accent transition-all delay-300 w-[15rem] flex items-center gap-2 cursor-none lg:mt-[10rem] mx-auto"><FaLinkedinIn className='inline-block mr-2'></FaLinkedinIn><a href="https://linkedin.com/in/harshith-p-09b357354" className='mt-1 cursor-none'>LinkedIn</a></button>
            <button className="bg-green-600 text-text px-8 py-4 rounded-lg text-2xl font-bold hover:bg-green-500 transition-all delay-300 w-[15rem] flex items-center gap-2 cursor-none mt-[2rem] mx-auto"><FaGithub className='inline-block mr-2'></FaGithub><a href="https://github.com/Harshith106" className='mt-1 cursor-none'>Github</a></button>
            <button className="bg-yellow-600 text-text px-8 py-4 rounded-lg text-2xl font-bold hover:bg-yellow-500 w-[15rem] transition-all delay-300 flex items-center gap-2 cursor-none mt-[2rem] mx-auto"><SiLeetcode className='inline-block mr-2'></SiLeetcode><a href="https://leetcode.com/u/harshith011/" className='mt-1 cursor-none'>Leetcode</a></button>
            <button className="bg-text text-gray-800 px-8 py-4 rounded-lg text-2xl font-bold hover:bg-gray-400 w-[15rem] transition-all delay-300 flex items-center gap-2 cursor-none mt-[2rem] mx-auto"><FaDownload className='inline-block mr-2'></FaDownload><a href="src/assets/Harshith.pdf" className='mt-1 cursor-none'>Resume</a></button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About