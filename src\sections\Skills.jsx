import React, { useRef, useEffect } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/all'
// Importing various icons for skills
import { 
  FaHtml5, FaCss3Alt, FaJs, FaReact, FaNode, FaPython,
  FaGitAlt, FaFigma, FaDocker, FaAws
} from 'react-icons/fa'
import { SiTailwindcss, SiMongodb, SiPostgresql, SiTypescript, SiNextdotjs } from 'react-icons/si'

const Skills = () => {
  const titleRef = useRef(null)
  const sectionRef = useRef(null)
  const skillRefs = useRef([])

  // Define skills with their icons and names
  const skills = [
    { id: 1, name: 'HTML', icon: <FaHtml5 className="text-4xl" /> },
    { id: 2, name: 'CSS', icon: <FaCss3Alt className="text-4xl" /> },
    { id: 3, name: 'JavaScript', icon: <FaJs className="text-4xl" /> },
    { id: 4, name: 'React', icon: <FaReact className="text-4xl" /> },
    { id: 5, name: 'Node.js', icon: <FaNode className="text-4xl" /> },
    { id: 6, name: 'Python', icon: <FaPython className="text-4xl" /> },
    { id: 7, name: 'TypeScript', icon: <SiTypescript className="text-4xl" /> },
    { id: 8, name: 'Tailwind', icon: <SiTailwindcss className="text-4xl" /> },
    { id: 9, name: 'MongoDB', icon: <SiMongodb className="text-4xl" /> },
    { id: 10, name: 'PostgreSQL', icon: <SiPostgresql className="text-4xl" /> },
    { id: 11, name: 'Git', icon: <FaGitAlt className="text-4xl" /> },
    { id: 12, name: 'Figma', icon: <FaFigma className="text-4xl" /> },
    { id: 13, name: 'Docker', icon: <FaDocker className="text-4xl" /> },
    { id: 14, name: 'AWS', icon: <FaAws className="text-4xl" /> },
    { id: 15, name: 'Next.js', icon: <SiNextdotjs className="text-4xl" /> },
  ]

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger)
    gsap.fromTo(titleRef.current, {
      y: 100, opacity: 0
    },
      {
        y: -300, opacity: 1, duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 40%",
          toggleActions: "play none none reverse"
        }
      })
  }, [])

  useEffect(() => {
    // Add event listeners for hover effects
    skillRefs.current.forEach((skillRef, index) => {
      if (!skillRef) return

      const handleMouseEnter = () => {
        // Blur all other skills
        skillRefs.current.forEach((otherRef, otherIndex) => {
          if (otherIndex !== index && otherRef) {
            gsap.to(otherRef, {
              filter: "blur(4px)",
              opacity: 0.5,
              duration: 0.3,
              ease: "power2.out"
            })
          }
        })

        // Scale the entire hovered skill item (the whole square)
        gsap.to(skillRef, {
          scale: 1.3,
          zIndex: 10,
          duration: 0.3,
          ease: "power2.out"
        })

        // Show skill name below the icon (inside the grid item)
        const nameElement = skillRef.querySelector('.skill-name')
        if (nameElement) {
          gsap.to(nameElement, {
            opacity: 1,
            y: 0,
            duration: 0.3,
            ease: "power2.out"
          })
        }

        // Send data to custom cursor
        const icon = skillRef.querySelector('svg')
        if (icon) {
          // Clone the icon to send to cursor
          const iconClone = icon.cloneNode(true)
          // Remove any existing IDs to avoid conflicts
          if (iconClone.id) iconClone.removeAttribute('id')
          document.body.setAttribute('data-skill-hover', 'true')
          document.body.setAttribute('data-skill-icon', iconClone.outerHTML)
          document.body.setAttribute('data-skill-name', skills[index].name)
        }
      }

      const handleMouseLeave = () => {
        // Remove blur from all skills
        skillRefs.current.forEach((otherRef) => {
          if (otherRef) {
            gsap.to(otherRef, {
              filter: "blur(0px)",
              opacity: 1,
              duration: 0.3,
              ease: "power2.out"
            })
          }
        })

        // Reset the entire skill item
        gsap.to(skillRef, {
          scale: 1,
          zIndex: 0,
          duration: 0.3,
          ease: "power2.out"
        })

        // Hide skill name
        const nameElement = skillRef.querySelector('.skill-name')
        if (nameElement) {
          gsap.to(nameElement, {
            opacity: 0,
            y: 10,
            duration: 0.3,
            ease: "power2.out"
          })
        }

        // Reset custom cursor
        document.body.removeAttribute('data-skill-hover')
        document.body.removeAttribute('data-skill-icon')
        document.body.removeAttribute('data-skill-name')
      }

      skillRef.addEventListener('mouseenter', handleMouseEnter)
      skillRef.addEventListener('mouseleave', handleMouseLeave)

      // Cleanup
      return () => {
        skillRef.removeEventListener('mouseenter', handleMouseEnter)
        skillRef.removeEventListener('mouseleave', handleMouseLeave)
      }
    })
  }, [skills])

  return (
    <section ref={sectionRef} className='h-screen relative overflow-hidden bg-background'>
      <div className="container mx-auto px-4 h-full flex flex-col items-center justify-center">
        <h1 ref={titleRef} className='text-4xl md:text-5xl lg:text-6xl font-bold text-text sm:mb-16 text-center opacity-0'>Skills</h1>
        
        {/* Grid container with responsive layout */}
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-5 gap-20 w-full max-w-6xl">
          {skills.map((skill, index) => (
            <div 
              key={skill.id} 
              ref={(el) => (skillRefs.current[index] = el)}
              className="border-2 border-secondary rounded-lg w-24 h-24 flex items-center justify-center relative cursor-none overflow-visible bg-background"
            >
              <div className="flex flex-col items-center">
                {skill.icon}
                <span className="skill-name text-text text-sm mt-2 opacity-0 absolute transition-opacity duration-300">
                  {skill.name}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills