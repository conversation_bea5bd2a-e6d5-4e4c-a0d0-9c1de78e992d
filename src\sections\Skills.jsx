import React, { useRef, useEffect, useMemo } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/all'
// Importing various icons for skills
import { 
  FaHtml5, FaCss3Alt, FaJs, FaReact, FaNode, FaPython,
  FaGitAlt, FaFigma, FaDocker, FaAws
} from 'react-icons/fa'
import { SiTailwindcss, SiMongodb, SiPostgresql, SiTypescript, SiNextdotjs } from 'react-icons/si'

const Skills = () => {
  const titleRef = useRef(null)
  const sectionRef = useRef(null)
  const skillRefs = useRef([])

  // Define skills with their icons and names
  const skills = useMemo(() => [
    { id: 1, name: 'HTML', icon: <FaHtml5 className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 2, name: 'CSS', icon: <FaCss3Alt className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 3, name: 'JavaScript', icon: <FaJs className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 4, name: 'React', icon: <FaReact className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 5, name: 'Node.js', icon: <FaNode className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 6, name: 'Python', icon: <FaPython className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 7, name: 'TypeScript', icon: <SiTypescript className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 8, name: 'Tailwind', icon: <SiTailwindcss className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 9, name: 'MongoDB', icon: <SiMongodb className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 10, name: 'PostgreSQL', icon: <SiPostgresql className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 11, name: 'Git', icon: <FaGitAlt className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 12, name: 'Figma', icon: <FaFigma className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 13, name: 'Docker', icon: <FaDocker className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 14, name: 'AWS', icon: <FaAws className="text-2xl md:text-3xl lg:text-4xl" /> },
    { id: 15, name: 'Next.js', icon: <SiNextdotjs className="text-2xl md:text-3xl lg:text-4xl" /> },
  ], [])

  useEffect(()=>{
    gsap.registerPlugin(ScrollTrigger)
      gsap.fromTo(titleRef.current ,{
        y:300 , opacity:0
      },
    {
      y:0,opacity:1,duration:0.8,
      scrollTrigger:{
        trigger:sectionRef.current,
        start:"top 40%",
        toggleActions:"play none none reverse"
      }
    })
  })

  useEffect(() => {
    // Check if device supports hover and is desktop
    const checkHoverSupport = () => {
      const supportsHover = window.matchMedia('(hover: hover)').matches
      const isLargeScreen = window.innerWidth >= 1024
      return supportsHover && isLargeScreen
    }

    const shouldEnableHover = checkHoverSupport()
    console.log('Hover setup:', { shouldEnableHover, screenWidth: window.innerWidth })

    // Store event listeners for cleanup
    const eventListeners = []

    // Always set up skill names first
    skillRefs.current.forEach((skillRef) => {
      if (!skillRef) return
      const nameElement = skillRef.querySelector('.skill-name')
      if (nameElement) {
        if (!shouldEnableHover) {
          // On mobile/tablet, show skill names permanently
          gsap.set(nameElement, { opacity: 1, y: 0 })
        } else {
          // On desktop, hide initially (will show on hover)
          gsap.set(nameElement, { opacity: 0, y: 10 })
        }
      }
    })

    // Only add hover effects on desktop
    if (shouldEnableHover) {
      skillRefs.current.forEach((skillRef, index) => {
        if (!skillRef) return

        const handleMouseEnter = () => {
          // Blur all other skills
          skillRefs.current.forEach((otherRef, otherIndex) => {
            if (otherIndex !== index && otherRef) {
              gsap.to(otherRef, {
                filter: "blur(4px)",
                opacity: 0.5,
                duration: 0.3,
                ease: "power2.out"
              })
            }
          })

          // Remove border and scale the entire hovered skill item
          gsap.to(skillRef, {
            scale: 1.8,
            zIndex: 10,
            borderColor: "transparent",
            duration: 0.8,
            ease: "power2.out"
          })

          // Scale the icon within the skill item
          const iconElement = skillRef.querySelector('svg')
          if (iconElement) {
            gsap.to(iconElement, {
              scale: 1.8,
              duration: 0.8,
              ease: "back.out(1.2)",
              transformOrigin: "center center"
            })
          }

          // Show skill name below the icon (inside the grid item)
          const nameElement = skillRef.querySelector('.skill-name')
          if (nameElement) {
            gsap.to(nameElement, {
              opacity: 1,
              scale: 1.3,
              y: 0,
              duration: 0.8,
              ease: "power2.out"
            })
          }

          // Signal custom cursor to transform (hide inner, scale outer)
          document.body.setAttribute('data-skill-hover', 'true')
        }

        const handleMouseLeave = () => {
          // Remove blur from all skills
          skillRefs.current.forEach((otherRef) => {
            if (otherRef) {
              gsap.to(otherRef, {
                filter: "blur(0px)",
                opacity: 1,
                duration: 0.3,
                ease: "power2.out"
              })
            }
          })

          // Reset the entire skill item and restore border
          gsap.to(skillRef, {
            scale: 1,
            zIndex: 0,
            borderColor: "hsl(213, 69%, 71%)", // Reset to secondary color
            duration: 0.3,
            ease: "power2.out"
          })

          // Reset the icon scale
          const iconElement = skillRef.querySelector('svg')
          if (iconElement) {
            gsap.to(iconElement, {
              scale: 1,
              duration: 0.6,
              ease: "back.out(1.1)",
              transformOrigin: "center center"
            })
          }

          // Hide skill name
          const nameElement = skillRef.querySelector('.skill-name')
          if (nameElement) {
            gsap.to(nameElement, {
              opacity: 0,
              y: 10,
              duration: 0.3,
              ease: "power2.out"
            })
          }

          // Reset custom cursor
          document.body.removeAttribute('data-skill-hover')
        }

        skillRef.addEventListener('mouseenter', handleMouseEnter)
        skillRef.addEventListener('mouseleave', handleMouseLeave)

        // Store for cleanup
        eventListeners.push({
          element: skillRef,
          enterHandler: handleMouseEnter,
          leaveHandler: handleMouseLeave
        })
      })
    }

    // Cleanup function
    return () => {
      eventListeners.forEach(({ element, enterHandler, leaveHandler }) => {
        element.removeEventListener('mouseenter', enterHandler)
        element.removeEventListener('mouseleave', leaveHandler)
      })
    }
  }, [skills])

  return (
    <section ref={sectionRef} className='min-h-screen relative overflow-hidden bg-background py-16 md:py-20'>
      <div className="container mx-auto px-4 h-full flex flex-col items-center justify-center">
        <h1 ref={titleRef} className='text-3xl md:text-5xl lg:text-6xl font-bold text-text mb-8 md:mb-16 text-center opacity-0'>Skills</h1>

        {/* Grid container with responsive layout */}
        <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-5 gap-4 sm:gap-6 md:gap-8 lg:gap-12 w-full max-w-6xl justify-items-center">
          {skills.map((skill, index) => (
            <div
              key={skill.id}
              ref={(el) => (skillRefs.current[index] = el)}
              className="border-2 border-secondary rounded-lg w-16 h-20 sm:w-20 sm:h-24 md:w-28 md:h-32 lg:w-40 lg:h-40 flex flex-col items-center justify-center relative cursor-none overflow-visible bg-background"
            >
              <div className="flex flex-col items-center justify-center h-full">
                <div className="flex-shrink-0">
                  {skill.icon}
                </div>
                <span className="skill-name text-text text-xs mt-1 md:mt-2 opacity-0 lg:opacity-0 md:opacity-100 sm:opacity-100 absolute bottom-1 md:bottom-2 whitespace-nowrap">
                  {skill.name}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills